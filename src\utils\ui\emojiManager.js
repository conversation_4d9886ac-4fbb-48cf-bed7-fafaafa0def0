import { EMOJIS, getDefaultEmojiForKey } from '../../config/constants.js';
import EmojiConfig from '../../models/EmojiConfig.js';
import { logger } from '../logging/logger.js';

/**
 * Classe para gerenciar emojis personalizados por servidor
 */
export class EmojiManager {
    constructor() {
        this.cache = new Map(); // Cache de configurações por guildId
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutos
    }

    /**
     * Obtém todos os emojis disponíveis com suas descrições
     * @returns {Object} Objeto com informações dos emojis
     */
    getAvailableEmojis() {
        return {
            // Emojis gerais
            SUCCESS: { emoji: EMOJIS.SUCCESS, description: 'Sucesso/Confirmação', category: 'Geral' },
            ERROR: { emoji: EMOJIS.ERROR, description: 'Erro/Falha', category: 'Geral' },
            WARNING: { emoji: EMOJIS.WARNING, description: 'Aviso/Atenção', category: 'Geral' },
            INFO: { emoji: EMOJIS.INFO, description: 'Informação', category: 'Geral' },
            LOADING: { emoji: EMOJIS.LOADING, description: 'Carregando/Processando', category: 'Geral' },
            
            // Emojis da loja
            MONEY: { emoji: EMOJIS.MONEY, description: 'Dinheiro/Preço', category: 'Loja' },
            CART: { emoji: EMOJIS.CART, description: 'Carrinho de compras', category: 'Loja' },
            PRODUCT: { emoji: EMOJIS.PRODUCT, description: 'Produto genérico', category: 'Loja' },
            DIGITAL: { emoji: EMOJIS.DIGITAL, description: 'Produto digital', category: 'Loja' },
            PHYSICAL: { emoji: EMOJIS.PHYSICAL, description: 'Produto físico', category: 'Loja' },
            SERVICE: { emoji: EMOJIS.SERVICE, description: 'Serviço', category: 'Loja' },
            SUBSCRIPTION: { emoji: EMOJIS.SUBSCRIPTION, description: 'Assinatura', category: 'Loja' },
            
            // Emojis de navegação
            PREVIOUS: { emoji: EMOJIS.PREVIOUS, description: 'Página anterior', category: 'Navegação' },
            NEXT: { emoji: EMOJIS.NEXT, description: 'Próxima página', category: 'Navegação' },
            REFRESH: { emoji: EMOJIS.REFRESH, description: 'Atualizar/Recarregar', category: 'Navegação' },
            HOME: { emoji: EMOJIS.HOME, description: 'Início/Home', category: 'Navegação' },
            BACK: { emoji: EMOJIS.BACK, description: 'Voltar', category: 'Navegação' },
            
            // Emojis de status
            ONLINE: { emoji: EMOJIS.ONLINE, description: 'Online/Ativo', category: 'Status' },
            OFFLINE: { emoji: EMOJIS.OFFLINE, description: 'Offline/Inativo', category: 'Status' },
            IDLE: { emoji: EMOJIS.IDLE, description: 'Ausente/Idle', category: 'Status' },
            DND: { emoji: EMOJIS.DND, description: 'Não perturbe', category: 'Status' }
        };
    }

    /**
     * Obtém a configuração de emojis para um servidor
     * @param {string} guildId - ID do servidor
     * @returns {Promise<Object>} Configuração de emojis
     */
    async getGuildEmojiConfig(guildId) {
        try {
            // Verifica cache primeiro
            const cached = this.cache.get(guildId);
            if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.config;
            }

            // Busca no banco de dados
            const config = await EmojiConfig.findByGuild(guildId);
            
            // Cria configuração padrão se não existir
            const emojiConfig = config?.customEmojis || {};
            
            // Atualiza cache
            this.cache.set(guildId, {
                config: emojiConfig,
                timestamp: Date.now()
            });

            return emojiConfig;
        } catch (error) {
            logger.error('Erro ao buscar configuração de emojis:', error);
            return {};
        }
    }

    /**
     * Obtém um emoji específico para um servidor
     * @param {string} guildId - ID do servidor
     * @param {string} emojiKey - Chave do emoji (ex: 'SUCCESS', 'ERROR')
     * @returns {Promise<string>} Emoji personalizado ou padrão
     */
    async getEmoji(guildId, emojiKey) {
        try {
            const config = await this.getGuildEmojiConfig(guildId);

            // Retorna emoji personalizado se configurado, senão retorna o padrão inteligente
            return config[emojiKey] || getDefaultEmojiForKey(emojiKey);
        } catch (error) {
            logger.error(`Erro ao buscar emoji ${emojiKey}:`, error);
            return getDefaultEmojiForKey(emojiKey);
        }
    }

    /**
     * Obtém todos os emojis para um servidor (personalizados + padrões)
     * @param {string} guildId - ID do servidor
     * @returns {Promise<Object>} Objeto com todos os emojis
     */
    async getAllEmojis(guildId) {
        try {
            const config = await this.getGuildEmojiConfig(guildId);
            const result = {};

            // Para cada emoji padrão, usa o personalizado se disponível
            for (const [key, defaultEmoji] of Object.entries(EMOJIS)) {
                result[key] = config[key] || defaultEmoji;
            }

            return result;
        } catch (error) {
            logger.error('Erro ao buscar todos os emojis:', error);
            return EMOJIS;
        }
    }

    /**
     * Valida se um emoji é válido
     * @param {string} emoji - Emoji a ser validado
     * @param {Object} guild - Objeto guild do Discord.js
     * @returns {Promise<Object>} Resultado da validação
     */
    async validateEmoji(emoji, guild) {
        try {
            // Remove espaços em branco
            emoji = emoji.trim();

            if (!emoji) {
                return { valid: false, error: 'Emoji não pode estar vazio' };
            }

            // Verifica se é um emoji Unicode padrão (regex simples)
            const unicodeEmojiRegex = /^[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[\u{1F900}-\u{1F9FF}]|[\u{1F018}-\u{1F270}]|[\u{238C}-\u{2454}]|[\u{20D0}-\u{20FF}]|[\u{FE0F}]|[\u{200D}]|[\u{E0020}-\u{E007F}]|[\u{1F004}]|[\u{1F0CF}]|[\u{1F170}-\u{1F251}]$/u;
            
            if (unicodeEmojiRegex.test(emoji)) {
                return { valid: true, type: 'unicode', emoji };
            }

            // Verifica se é um emoji customizado do Discord (<:name:id> ou <a:name:id>)
            const customEmojiRegex = /^<a?:(\w+):(\d+)>$/;
            const match = emoji.match(customEmojiRegex);

            if (match) {
                const [, name, id] = match;
                
                // Verifica se o bot tem acesso ao emoji (está no mesmo servidor ou é global)
                const emojiObj = guild.emojis.cache.get(id);
                
                if (emojiObj) {
                    return { 
                        valid: true, 
                        type: 'custom', 
                        emoji,
                        name: emojiObj.name,
                        id: emojiObj.id,
                        animated: emojiObj.animated
                    };
                } else {
                    return { 
                        valid: false, 
                        error: 'Emoji customizado não encontrado ou bot não tem acesso a ele' 
                    };
                }
            }

            // Tenta validar como emoji Unicode mais complexo
            try {
                // Verifica se contém caracteres de emoji
                const hasEmoji = /[\u{1F000}-\u{1F9FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u.test(emoji);
                if (hasEmoji && emoji.length <= 10) { // Limite razoável para emojis compostos
                    return { valid: true, type: 'unicode', emoji };
                }
            } catch (error) {
                // Ignora erros de regex
            }

            return { 
                valid: false, 
                error: 'Formato de emoji inválido. Use emojis Unicode padrão ou emojis customizados do servidor.' 
            };

        } catch (error) {
            logger.error('Erro ao validar emoji:', error);
            return { valid: false, error: 'Erro interno ao validar emoji' };
        }
    }

    /**
     * Atualiza um emoji personalizado para um servidor
     * @param {string} guildId - ID do servidor
     * @param {string} emojiKey - Chave do emoji
     * @param {string} emojiValue - Novo valor do emoji
     * @param {string} modifiedBy - ID do usuário que modificou
     * @returns {Promise<boolean>} Sucesso da operação
     */
    async updateEmoji(guildId, emojiKey, emojiValue, modifiedBy) {
        try {
            await EmojiConfig.createOrUpdate(guildId, emojiKey, emojiValue, modifiedBy);
            
            // Limpa cache para forçar reload
            this.cache.delete(guildId);
            
            logger.info(`Emoji ${emojiKey} atualizado para ${emojiValue} no servidor ${guildId} por ${modifiedBy}`);
            return true;
        } catch (error) {
            logger.error('Erro ao atualizar emoji:', error);
            return false;
        }
    }

    /**
     * Reseta um emoji para o padrão
     * @param {string} guildId - ID do servidor
     * @param {string} emojiKey - Chave do emoji
     * @param {string} modifiedBy - ID do usuário que modificou
     * @returns {Promise<boolean>} Sucesso da operação
     */
    async resetEmoji(guildId, emojiKey, modifiedBy) {
        try {
            const config = await EmojiConfig.findByGuild(guildId);
            if (config) {
                await config.resetEmoji(emojiKey, modifiedBy);
            }
            
            // Limpa cache
            this.cache.delete(guildId);
            
            logger.info(`Emoji ${emojiKey} resetado no servidor ${guildId} por ${modifiedBy}`);
            return true;
        } catch (error) {
            logger.error('Erro ao resetar emoji:', error);
            return false;
        }
    }

    /**
     * Limpa o cache de um servidor específico
     * @param {string} guildId - ID do servidor
     */
    clearCache(guildId) {
        this.cache.delete(guildId);
    }

    /**
     * Limpa todo o cache
     */
    clearAllCache() {
        this.cache.clear();
    }
}

// Instância singleton
export const emojiManager = new EmojiManager();
