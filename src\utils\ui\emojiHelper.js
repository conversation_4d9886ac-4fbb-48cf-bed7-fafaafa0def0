import { EMOJIS, getEmojiForGuild, getEmojisForGuild, getDefaultEmojiForKey } from '../../config/constants.js';

/**
 * Classe helper para facilitar o uso de emojis personalizados
 */
export class EmojiHelper {
    /**
     * Obtém um emoji específico para um servidor
     * @param {string} guildId - ID do servidor
     * @param {string} emojiKey - Chave do emoji
     * @returns {Promise<string>} Emoji personalizado ou padrão
     */
    static async get(guildId, emojiKey) {
        if (!guildId) {
            return getDefaultEmojiForKey(emojiKey);
        }
        return await getEmojiForGuild(guildId, emojiKey);
    }

    /**
     * Obtém todos os emojis para um servidor
     * @param {string} guildId - ID do servidor
     * @returns {Promise<Object>} Objeto com todos os emojis
     */
    static async getAll(guildId) {
        if (!guildId) {
            return EMOJIS;
        }
        return await getEmojisForGuild(guildId);
    }

    /**
     * Obtém múltiplos emojis específicos para um servidor
     * @param {string} guildId - ID do servidor
     * @param {string[]} emojiKeys - Array com chaves dos emojis
     * @returns {Promise<Object>} Objeto com os emojis solicitados
     */
    static async getMultiple(guildId, emojiKeys) {
        const result = {};
        
        if (!guildId) {
            for (const key of emojiKeys) {
                result[key] = getDefaultEmojiForKey(key);
            }
            return result;
        }

        for (const key of emojiKeys) {
            result[key] = await getEmojiForGuild(guildId, key);
        }
        
        return result;
    }

    /**
     * Cria um objeto de emojis a partir de uma interaction
     * @param {Interaction} interaction - Interação do Discord
     * @returns {Promise<Object>} Objeto com todos os emojis
     */
    static async fromInteraction(interaction) {
        const guildId = interaction.guild?.id;
        return await this.getAll(guildId);
    }

    /**
     * Obtém um emoji específico a partir de uma interaction
     * @param {Interaction} interaction - Interação do Discord
     * @param {string} emojiKey - Chave do emoji
     * @returns {Promise<string>} Emoji personalizado ou padrão
     */
    static async getFromInteraction(interaction, emojiKey) {
        const guildId = interaction.guild?.id;
        return await this.get(guildId, emojiKey);
    }

    /**
     * Substitui placeholders de emoji em uma string
     * @param {string} text - Texto com placeholders no formato {EMOJI_KEY}
     * @param {string} guildId - ID do servidor
     * @returns {Promise<string>} Texto com emojis substituídos
     */
    static async replacePlaceholders(text, guildId) {
        if (!text || typeof text !== 'string') {
            return text;
        }

        // Encontra todos os placeholders no formato {EMOJI_KEY}
        const placeholders = text.match(/\{([A-Z_]+)\}/g);
        
        if (!placeholders) {
            return text;
        }

        let result = text;
        
        for (const placeholder of placeholders) {
            const emojiKey = placeholder.replace(/[{}]/g, '');
            const emoji = await this.get(guildId, emojiKey);
            result = result.replace(placeholder, emoji);
        }
        
        return result;
    }

    /**
     * Cria uma função de template para emojis de um servidor específico
     * @param {string} guildId - ID do servidor
     * @returns {Function} Função que retorna emojis para o servidor
     */
    static createTemplate(guildId) {
        const cache = new Map();
        let cacheTime = 0;
        const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

        return async (emojiKey) => {
            const now = Date.now();
            
            // Verifica se o cache ainda é válido
            if (now - cacheTime > CACHE_DURATION) {
                cache.clear();
                cacheTime = now;
            }

            // Verifica se o emoji está no cache
            if (cache.has(emojiKey)) {
                return cache.get(emojiKey);
            }

            // Busca o emoji e adiciona ao cache
            const emoji = await this.get(guildId, emojiKey);
            cache.set(emojiKey, emoji);
            
            return emoji;
        };
    }

    /**
     * Valida se uma chave de emoji existe
     * @param {string} emojiKey - Chave do emoji
     * @returns {boolean} True se a chave existe
     */
    static isValidKey(emojiKey) {
        return emojiKey in EMOJIS;
    }

    /**
     * Obtém todas as chaves de emoji disponíveis
     * @returns {string[]} Array com todas as chaves
     */
    static getAvailableKeys() {
        return Object.keys(EMOJIS);
    }

    /**
     * Obtém emojis por categoria
     * @param {string} guildId - ID do servidor
     * @param {string} category - Categoria desejada
     * @returns {Promise<Object>} Objeto com emojis da categoria
     */
    static async getByCategory(guildId, category) {
        // Importação dinâmica para evitar dependência circular
        const { emojiManager } = await import('./emojiManager.js');
        const availableEmojis = emojiManager.getAvailableEmojis();
        
        const result = {};
        
        for (const [key, info] of Object.entries(availableEmojis)) {
            if (info.category === category) {
                result[key] = await this.get(guildId, key);
            }
        }
        
        return result;
    }
}

// Funções de conveniência para uso direto
export const emoji = EmojiHelper;

/**
 * Função de conveniência para obter um emoji
 * @param {string} guildId - ID do servidor
 * @param {string} emojiKey - Chave do emoji
 * @returns {Promise<string>} Emoji personalizado ou padrão
 */
export async function getEmoji(guildId, emojiKey) {
    return await EmojiHelper.get(guildId, emojiKey);
}

/**
 * Função de conveniência para obter emojis de uma interaction
 * @param {Interaction} interaction - Interação do Discord
 * @param {string} emojiKey - Chave do emoji
 * @returns {Promise<string>} Emoji personalizado ou padrão
 */
export async function getEmojiFromInteraction(interaction, emojiKey) {
    return await EmojiHelper.getFromInteraction(interaction, emojiKey);
}
