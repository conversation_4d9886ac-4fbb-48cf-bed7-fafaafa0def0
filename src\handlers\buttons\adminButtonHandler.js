import { PermissionFlagsBits } from 'discord.js';
import { logger } from '../../utils/logging/logger.js';
import { ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder, StringSelectMenuBuilder, StringSelectMenuOptionBuilder } from 'discord.js';
import Product from '../../models/Product.js';
import Store from '../../models/Store.js';

/**
 * Handler para botões administrativos
 */
export class AdminButtonHandler {
    /**
     * Manipula botões administrativos
     * @param {Object} interaction - Interação do Discord
     * @param {string} action - Ação do botão
     * @param {string} id - ID relacionado ao botão
     */
    static async handleAdminButton(interaction, action, id) {
        try {
            // Verificação de permissões
            if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
                return await interaction.reply({
                    content: '❌ Apenas administradores podem usar este botão.',
                    ephemeral: true
                });
            }

            logger.info(`Botão administrativo clicado: ${action} (ID: ${id}) por ${interaction.user.tag}`);

            switch (action) {
                case 'manage':
                    await this.handleManageButton(interaction, id);
                    break;
                case 'delete':
                    await this.handleDeleteButton(interaction, id);
                    break;
                case 'edit':
                    await this.handleEditButton(interaction, id);
                    break;
                default:
                    await interaction.reply({
                        content: '❌ Ação administrativa não reconhecida.',
                        ephemeral: true
                    });
            }
        } catch (error) {
            logger.error('Erro no handler de botão administrativo:', error);
            
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro ao processar sua solicitação.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula botão de gerenciamento
     * @param {Object} interaction - Interação do Discord
     * @param {string} id - ID do item
     */
    static async handleManageButton(interaction, id) {
        await interaction.reply({
            content: `⚙️ Funcionalidade de gerenciamento será implementada em breve! (ID: ${id})`,
            ephemeral: true
        });
    }

    /**
     * Manipula botão de exclusão
     * @param {Object} interaction - Interação do Discord
     * @param {string} id - ID do item
     */
    static async handleDeleteButton(interaction, id) {
        await interaction.reply({
            content: `🗑️ Funcionalidade de exclusão será implementada em breve! (ID: ${id})`,
            ephemeral: true
        });
    }

    /**
     * Manipula botão de edição
     * @param {Object} interaction - Interação do Discord
     * @param {string} id - ID do item
     */
    static async handleEditButton(interaction, id) {
        await interaction.reply({
            content: `✏️ Funcionalidade de edição será implementada em breve! (ID: ${id})`,
            ephemeral: true
        });
    }

    /**
     * Manipula botões de edição de produto
     * @param {Object} interaction - Interação do Discord
     * @param {string} customId - ID customizado completo
     */
    static async handleEditProductButton(interaction, customId) {
        try {
            // Verificação de permissões
            if (!interaction.member.permissions.has('Administrator')) {
                return await interaction.reply({
                    content: '❌ Apenas administradores podem editar produtos.',
                    ephemeral: true
                });
            }

            // Parse do customId: edit_product_basic_productId, edit_product_price_productId, etc.
            const parts = customId.split('_');
            const editType = parts[2]; // basic, price, status, category
            const productId = parts[3];

            // Busca o produto
            const product = await Product.findById(productId).populate('storeId');
            if (!product) {
                return await interaction.reply({
                    content: '❌ Produto não encontrado.',
                    ephemeral: true
                });
            }

            // Verifica se a loja pertence ao servidor atual
            if (product.storeId.guildId !== interaction.guild.id) {
                return await interaction.reply({
                    content: '❌ Este produto não pertence a este servidor.',
                    ephemeral: true
                });
            }

            logger.info(`Botão de edição de produto clicado: ${editType} para produto ${product.name} por ${interaction.user.tag}`);

            switch (editType) {
                case 'basic':
                    await this.showEditBasicModal(interaction, product);
                    break;
                case 'price':
                    await this.showEditPriceModal(interaction, product);
                    break;
                case 'status':
                    await this.showEditStatusSelect(interaction, product);
                    break;
                case 'category':
                    await this.showEditCategorySelect(interaction, product);
                    break;
                default:
                    await interaction.reply({
                        content: '❌ Tipo de edição não reconhecido.',
                        ephemeral: true
                    });
            }

        } catch (error) {
            logger.error('Erro no handler de botão de edição de produto:', error);

            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro ao processar a edição do produto.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Mostra modal para editar informações básicas do produto
     */
    static async showEditBasicModal(interaction, product) {
        const modal = new ModalBuilder()
            .setCustomId(`edit_product_basic_modal_${product._id}`)
            .setTitle(`Editar ${product.name}`);

        const nameInput = new TextInputBuilder()
            .setCustomId('product_name')
            .setLabel('Nome do Produto')
            .setStyle(TextInputStyle.Short)
            .setValue(product.name)
            .setRequired(true)
            .setMinLength(3)
            .setMaxLength(100);

        const descriptionInput = new TextInputBuilder()
            .setCustomId('product_description')
            .setLabel('Descrição do Produto')
            .setStyle(TextInputStyle.Paragraph)
            .setValue(product.description || '')
            .setRequired(false)
            .setMaxLength(1000);

        const emojiInput = new TextInputBuilder()
            .setCustomId('product_emoji')
            .setLabel('Emoji do Produto (opcional)')
            .setStyle(TextInputStyle.Short)
            .setValue(product.emoji || '')
            .setRequired(false)
            .setMaxLength(50);

        const nameRow = new ActionRowBuilder().addComponents(nameInput);
        const descriptionRow = new ActionRowBuilder().addComponents(descriptionInput);
        const emojiRow = new ActionRowBuilder().addComponents(emojiInput);

        modal.addComponents(nameRow, descriptionRow, emojiRow);
        await interaction.showModal(modal);
    }

    /**
     * Mostra modal para editar preço do produto
     */
    static async showEditPriceModal(interaction, product) {
        const modal = new ModalBuilder()
            .setCustomId(`edit_product_price_modal_${product._id}`)
            .setTitle(`Alterar Preço - ${product.name}`);

        const priceInput = new TextInputBuilder()
            .setCustomId('product_price')
            .setLabel('Novo Preço (R$)')
            .setStyle(TextInputStyle.Short)
            .setValue(product.price.toString())
            .setRequired(true)
            .setMaxLength(20);

        const originalPriceInput = new TextInputBuilder()
            .setCustomId('product_original_price')
            .setLabel('Preço Original (opcional - para promoções)')
            .setStyle(TextInputStyle.Short)
            .setValue(product.originalPrice ? product.originalPrice.toString() : '')
            .setRequired(false)
            .setMaxLength(20);

        const priceRow = new ActionRowBuilder().addComponents(priceInput);
        const originalPriceRow = new ActionRowBuilder().addComponents(originalPriceInput);

        modal.addComponents(priceRow, originalPriceRow);
        await interaction.showModal(modal);
    }

    /**
     * Mostra select menu para alterar status do produto
     */
    static async showEditStatusSelect(interaction, product) {
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId(`edit_product_status_select_${product._id}`)
            .setPlaceholder('Selecione o novo status...')
            .setMinValues(1)
            .setMaxValues(1);

        const statusOptions = [
            { value: 'active', label: '🟢 Ativo', description: 'Produto disponível para venda' },
            { value: 'inactive', label: '🟡 Inativo', description: 'Produto temporariamente indisponível' },
            { value: 'out_of_stock', label: '🔴 Sem Estoque', description: 'Produto sem estoque disponível' },
            { value: 'discontinued', label: '⚫ Descontinuado', description: 'Produto não será mais vendido' }
        ];

        for (const option of statusOptions) {
            selectMenu.addOptions({
                label: option.label,
                description: option.description,
                value: option.value,
                default: product.status === option.value
            });
        }

        const row = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.reply({
            content: `🔄 **Alterar Status - ${product.name}**\n\nStatus atual: ${this.getStatusEmoji(product.status)} ${product.status}\n\nSelecione o novo status:`,
            components: [row],
            ephemeral: true
        });
    }

    /**
     * Mostra select menu para alterar categoria do produto
     */
    static async showEditCategorySelect(interaction, product) {
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId(`edit_product_category_select_${product._id}`)
            .setPlaceholder('Selecione a nova categoria...')
            .setMinValues(1)
            .setMaxValues(1);

        const categoryOptions = [
            { value: 'digital', label: '💻 Digital', description: 'Produtos digitais (contas, códigos, etc.)' },
            { value: 'physical', label: '📦 Físico', description: 'Produtos físicos que precisam de envio' },
            { value: 'service', label: '🛠️ Serviço', description: 'Serviços prestados' },
            { value: 'subscription', label: '🔄 Assinatura', description: 'Produtos de assinatura recorrente' }
        ];

        for (const option of categoryOptions) {
            selectMenu.addOptions({
                label: option.label,
                description: option.description,
                value: option.value,
                default: product.category === option.value
            });
        }

        const row = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.reply({
            content: `📂 **Alterar Categoria - ${product.name}**\n\nCategoria atual: ${this.getCategoryEmoji(product.category)} ${product.category}\n\nSelecione a nova categoria:`,
            components: [row],
            ephemeral: true
        });
    }

    /**
     * Retorna emoji para status
     */
    static getStatusEmoji(status) {
        const emojis = {
            'active': '🟢',
            'inactive': '🟡',
            'out_of_stock': '🔴',
            'discontinued': '⚫'
        };
        return emojis[status] || '🔹';
    }

    /**
     * Retorna emoji para categoria
     */
    static getCategoryEmoji(category) {
        const emojis = {
            'digital': '💻',
            'physical': '📦',
            'service': '🛠️',
            'subscription': '🔄'
        };
        return emojis[category] || '📂';
    }
}
