/**
 * Constantes e configurações do bot
 */

export const BOT_CONFIG = {
    // Configurações gerais
    PREFIX: process.env.BOT_PREFIX || '!',
    NODE_ENV: process.env.NODE_ENV || 'development',
    OWNER_ID: process.env.BOT_OWNER_ID, // Discord ID do dono do bot
    
    // Configurações de paginação
    ITEMS_PER_PAGE: 5,
    MAX_EMBED_FIELDS: 25,
    
    // Limites de caracteres
    MAX_EMBED_DESCRIPTION: 4096,
    MAX_EMBED_FIELD_VALUE: 1024,
    MAX_EMBED_TITLE: 256,
    
    // Timeouts (em milissegundos)
    INTERACTION_TIMEOUT: 15 * 60 * 1000, // 15 minutos
    BUTTON_TIMEOUT: 5 * 60 * 1000,       // 5 minutos
    MODAL_TIMEOUT: 10 * 60 * 1000,       // 10 minutos
    
    // Configurações da loja
    STORE: {
        DEFAULT_CURRENCY: 'BRL',
        CURRENCY_SYMBOL: 'R$',
        MAX_CART_ITEMS: 10,
        MAX_QUANTITY_PER_ITEM: 99,
        MIN_ORDER_VALUE: 1.00,
        MAX_ORDER_VALUE: 10000.00
    }
};

export const COLORS = {
    // Cores principais
    PRIMARY: '#0099ff',
    SECONDARY: '#6c757d',
    SUCCESS: '#00ff00',
    WARNING: '#ffff00',
    ERROR: '#ff0000',
    DANGER: '#dc3545',
    INFO: '#00ffff',
    
    // Cores específicas
    MONEY: '#85bb65',
    PREMIUM: '#f1c40f',
    ADMIN: '#e74c3c',
    MODERATOR: '#3498db'
};

export const EMOJIS = {
    // Emojis gerais
    SUCCESS: '✅',
    ERROR: '❌',
    WARNING: '⚠️',
    INFO: 'ℹ️',
    LOADING: '⏳',

    // Emojis da loja
    MONEY: '💰',
    CART: '🛒',
    PRODUCT: '📦',
    DIGITAL: '💾',
    PHYSICAL: '📦',
    SERVICE: '🛠️',
    SUBSCRIPTION: '🔄',

    // Emojis de navegação
    PREVIOUS: '◀️',
    NEXT: '▶️',
    REFRESH: '🔄',
    HOME: '🏠',
    BACK: '↩️',

    // Emojis de status
    ONLINE: '🟢',
    OFFLINE: '🔴',
    IDLE: '🟡',
    DND: '⛔'
};

/**
 * Obtém um emoji padrão baseado na chave quando não há emoji personalizado configurado
 * @param {string} emojiKey - Chave do emoji
 * @returns {string} Emoji Unicode apropriado para a chave
 */
export function getDefaultEmojiForKey(emojiKey) {
    // Se a chave existe no objeto EMOJIS, retorna o emoji padrão
    if (EMOJIS[emojiKey]) {
        return EMOJIS[emojiKey];
    }

    // Mapeamento inteligente baseado em padrões na chave
    const key = emojiKey.toUpperCase();

    // Emojis de sucesso/confirmação
    if (key.includes('SUCCESS') || key.includes('CONFIRM') || key.includes('APPROVE') || key.includes('ACCEPT')) {
        return '✅';
    }

    // Emojis de erro/falha
    if (key.includes('ERROR') || key.includes('FAIL') || key.includes('DENY') || key.includes('REJECT') || key.includes('CANCEL')) {
        return '❌';
    }

    // Emojis de aviso/atenção
    if (key.includes('WARNING') || key.includes('WARN') || key.includes('ALERT') || key.includes('CAUTION')) {
        return '⚠️';
    }

    // Emojis de informação
    if (key.includes('INFO') || key.includes('HELP') || key.includes('QUESTION') || key.includes('ABOUT')) {
        return 'ℹ️';
    }

    // Emojis de carregamento/processamento
    if (key.includes('LOADING') || key.includes('PROCESS') || key.includes('WAIT') || key.includes('PENDING')) {
        return '⏳';
    }

    // Emojis relacionados a dinheiro/pagamento
    if (key.includes('MONEY') || key.includes('PAYMENT') || key.includes('PAY') || key.includes('COIN') || key.includes('CASH') || key.includes('PRICE')) {
        return '💰';
    }

    // Emojis de carrinho/compras
    if (key.includes('CART') || key.includes('SHOP') || key.includes('BUY') || key.includes('PURCHASE')) {
        return '🛒';
    }

    // Emojis de produtos/itens
    if (key.includes('PRODUCT') || key.includes('ITEM') || key.includes('PACKAGE') || key.includes('BOX')) {
        return '📦';
    }

    // Emojis de usuário/pessoa (deve vir antes de DIGITAL para evitar conflito com USER)
    if (key.includes('USER') || key.includes('PERSON') || key.includes('MEMBER') || key.includes('PROFILE')) {
        return '👤';
    }

    // Emojis digitais/tecnologia
    if (key.includes('DIGITAL') || key.includes('DOWNLOAD') || key.includes('FILE') || key.includes('DATA')) {
        return '💾';
    }

    // Emojis de serviços/ferramentas
    if (key.includes('SERVICE') || key.includes('TOOL') || key.includes('REPAIR') || key.includes('FIX')) {
        return '🛠️';
    }

    // Emojis de assinatura/recorrência
    if (key.includes('SUBSCRIPTION') || key.includes('RECURRING') || key.includes('REPEAT') || key.includes('CYCLE')) {
        return '🔄';
    }

    // Emojis de navegação - anterior
    if (key.includes('PREVIOUS') || key.includes('PREV') || key.includes('BACK') || key.includes('LEFT')) {
        return '◀️';
    }

    // Emojis de navegação - próximo
    if (key.includes('NEXT') || key.includes('FORWARD') || key.includes('RIGHT')) {
        return '▶️';
    }

    // Emojis de atualização/refresh
    if (key.includes('REFRESH') || key.includes('RELOAD') || key.includes('UPDATE')) {
        return '🔄';
    }

    // Emojis de casa/início
    if (key.includes('HOME') || key.includes('MAIN') || key.includes('START')) {
        return '🏠';
    }

    // Emojis de configuração/ajustes
    if (key.includes('CONFIG') || key.includes('SETTING') || key.includes('OPTION') || key.includes('GEAR')) {
        return '⚙️';
    }

    // Emojis de lista/menu
    if (key.includes('LIST') || key.includes('MENU') || key.includes('CATALOG') || key.includes('INDEX')) {
        return '📋';
    }

    // Emojis de busca/pesquisa
    if (key.includes('SEARCH') || key.includes('FIND') || key.includes('LOOK')) {
        return '🔍';
    }

    // Emojis de estatísticas/gráficos
    if (key.includes('STATS') || key.includes('CHART') || key.includes('GRAPH') || key.includes('ANALYTICS')) {
        return '📊';
    }

    // Emojis de notificação/sino
    if (key.includes('NOTIFICATION') || key.includes('NOTIFY') || key.includes('BELL') || key.includes('ALERT')) {
        return '🔔';
    }

    // Emojis de bloqueio/segurança
    if (key.includes('LOCK') || key.includes('SECURE') || key.includes('PRIVATE') || key.includes('PROTECTED')) {
        return '🔒';
    }

    // Emojis de desbloqueio/público
    if (key.includes('UNLOCK') || key.includes('PUBLIC') || key.includes('OPEN')) {
        return '🔓';
    }

    // Emojis de administração/admin
    if (key.includes('ADMIN') || key.includes('MANAGE') || key.includes('CONTROL')) {
        return '👑';
    }

    // Emojis de moderação
    if (key.includes('MOD') || key.includes('MODERATE')) {
        return '🛡️';
    }

    // Fallback padrão para chaves não mapeadas - usa um emoji neutro ao invés de ❓
    return '🔹';
}

/**
 * Obtém emojis personalizados para um servidor específico
 * @param {string} guildId - ID do servidor
 * @returns {Promise<Object>} Objeto com emojis personalizados ou padrões
 */
export async function getEmojisForGuild(guildId) {
    try {
        // Importação dinâmica para evitar dependência circular
        const { emojiManager } = await import('../utils/ui/emojiManager.js');
        return await emojiManager.getAllEmojis(guildId);
    } catch (error) {
        // Em caso de erro, retorna emojis padrões
        console.error('Erro ao carregar emojis personalizados:', error);
        return EMOJIS;
    }
}

/**
 * Obtém um emoji específico para um servidor
 * @param {string} guildId - ID do servidor
 * @param {string} emojiKey - Chave do emoji
 * @returns {Promise<string>} Emoji personalizado ou padrão
 */
export async function getEmojiForGuild(guildId, emojiKey) {
    try {
        // Importação dinâmica para evitar dependência circular
        const { emojiManager } = await import('../utils/ui/emojiManager.js');
        return await emojiManager.getEmoji(guildId, emojiKey);
    } catch (error) {
        // Em caso de erro, retorna emoji padrão inteligente
        console.error(`Erro ao carregar emoji ${emojiKey}:`, error);
        return getDefaultEmojiForKey(emojiKey);
    }
}

export const PERMISSIONS = {
    // Permissões do Discord
    ADMINISTRATOR: 'Administrator',
    MANAGE_GUILD: 'ManageGuild',
    MANAGE_CHANNELS: 'ManageChannels',
    MANAGE_MESSAGES: 'ManageMessages',
    KICK_MEMBERS: 'KickMembers',
    BAN_MEMBERS: 'BanMembers',
    
    // Níveis de acesso customizados
    LEVELS: {
        USER: 0,
        MODERATOR: 1,
        ADMIN: 2,
        OWNER: 3
    }
};

export const DATABASE = {
    // Configurações do MongoDB
    CONNECTION_TIMEOUT: 30000,
    MAX_POOL_SIZE: 10,
    MIN_POOL_SIZE: 1,
    
    // Configurações de cache
    CACHE_TTL: 5 * 60 * 1000, // 5 minutos
    
    // Limites de consulta
    MAX_QUERY_LIMIT: 100,
    DEFAULT_QUERY_LIMIT: 20
};

export const VALIDATION = {
    // Validações de usuário
    USERNAME: {
        MIN_LENGTH: 2,
        MAX_LENGTH: 32
    },
    
    // Validações de produto
    PRODUCT_NAME: {
        MIN_LENGTH: 3,
        MAX_LENGTH: 100
    },
    PRODUCT_DESCRIPTION: {
        MIN_LENGTH: 10,
        MAX_LENGTH: 1000
    },
    PRODUCT_PRICE: {
        MIN: 0.01,
        MAX: 999999.99
    },
    
    // Validações de pedido
    ORDER_NOTES: {
        MAX_LENGTH: 500
    },

    // Validações de estoque
    STOCK_CONTENT: {
        MAX_TOTAL_LENGTH: 4000,
        MAX_LINE_LENGTH: 500
    }
};

export const MESSAGES = {
    // Mensagens de erro comuns
    ERRORS: {
        GENERIC: 'Ocorreu um erro inesperado. Tente novamente mais tarde.',
        PERMISSION_DENIED: 'Você não tem permissão para executar esta ação.',
        USER_NOT_FOUND: 'Usuário não encontrado.',
        PRODUCT_NOT_FOUND: 'Produto não encontrado.',
        INSUFFICIENT_BALANCE: 'Saldo insuficiente.',
        OUT_OF_STOCK: 'Produto fora de estoque.',
        INVALID_QUANTITY: 'Quantidade inválida.',
        DATABASE_ERROR: 'Erro de conexão com o banco de dados.'
    },
    
    // Mensagens de sucesso
    SUCCESS: {
        COMMAND_EXECUTED: 'Comando executado com sucesso!',
        PRODUCT_ADDED: 'Produto adicionado com sucesso!',
        ORDER_CREATED: 'Pedido criado com sucesso!',
        PAYMENT_APPROVED: 'Pagamento aprovado!',
        BALANCE_UPDATED: 'Saldo atualizado com sucesso!'
    },
    
    // Mensagens informativas
    INFO: {
        PROCESSING: 'Processando sua solicitação...',
        PLEASE_WAIT: 'Por favor, aguarde...',
        NO_RESULTS: 'Nenhum resultado encontrado.',
        EMPTY_CART: 'Seu carrinho está vazio.',
        MAINTENANCE: 'Sistema em manutenção. Tente novamente mais tarde.'
    }
};
